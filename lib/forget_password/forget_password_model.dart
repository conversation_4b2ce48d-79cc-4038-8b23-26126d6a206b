import 'package:aslaa/flutter_flow/flutter_flow_model.dart';

import 'package:flutter/material.dart';

class ForgetPasswordModel extends FlutterFlowModel {
  ///  State fields for stateful widgets in this page.

  // State field(s) for mobile widget.
  TextEditingController? mobileController;
  String? Function(BuildContext, String?)? mobileControllerValidator;
  // State field(s) for PinCode widget.
  TextEditingController? pinCodeController;
  // State field(s) for password widget.
  TextEditingController? passwordController;
  late bool passwordVisibility;
  String? Function(BuildContext, String?)? passwordControllerValidator;

  /// Initialization and disposal methods.

  void initState(BuildContext context) {
    pinCodeController = TextEditingController();
    passwordVisibility = false;
  }

  void dispose() {
    mobileController?.dispose();
    pinCodeController?.dispose();
    passwordController?.dispose();
  }

  /// Additional helper methods are added here.
}
