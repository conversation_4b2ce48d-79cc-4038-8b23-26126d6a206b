// lib/models/device.dart

import 'package:flutter/foundation.dart';

/// Represents a device with its properties.
class Device {
  final String id;
  String deviceNumber;
  String deviceName;
  final String phoneNumber;
  String type;
  String uix;
  bool rentable = false;
  String renter = "";
  int interval = 10 * 60;
  bool isDefault;
  int rssi; // New field for RSSI with default value

  /// Constructor for the Device class.
  Device({
    required this.id,
    required this.deviceNumber,
    required this.phoneNumber,
    required this.type,
    this.deviceName = "",
    this.isDefault = false,
    required this.uix,
    int? rssi, // Allow nullable rssi
  }) : rssi = rssi ?? -100; // Default value for RSSI

  /// Factory constructor to create a Device instance from a map.
  factory Device.fromMap(Map<String, dynamic>? json) {
    debugPrint("Device.fromMap: \$json"); // Debug statement

    if (json == null) {
      return Device.fromVirtual();
    } else {
      return Device(
        phoneNumber: json['phoneNumber'] ?? '',
        id: json['_id'] ?? '',
        deviceNumber: json['deviceNumber'] ?? json['phoneNumber'] ?? '',
        type: json['type'] ?? 'Unknown',
        uix: json['uix'] ?? 'Unknown',
        deviceName: json['deviceName'] ?? '',
        isDefault: json['isDefault'] ?? false,
        rssi: json['rssi'] as int?, // Handle RSSI from map
      );
    }
  }

  /// Factory constructor to create a virtual Device instance.
  factory Device.fromVirtual() {
    return Device(
      phoneNumber: '',
      id: '',
      deviceNumber: '',
      type: '4G',
      uix: 'CarV1.2',
      deviceName: 'Virtual Device',
      isDefault: true,
      rssi: -100, // Default value for virtual device
    );
  }

  /// Converts the Device instance to a map.
  Map<String, dynamic> toMap() {
    return {
      "phoneNumber": phoneNumber,
      "deviceNumber": deviceNumber,
      "deviceName": deviceName,
      "type": type,
      "uix": uix,
      "rentable": rentable,
      "renter": renter,
      "isDefault": isDefault,
      "rssi": rssi, // Include RSSI in map
    };
  }
}

/// Represents the status of a device.
class DeviceStatus {
  bool online = false;
  bool gps = false;
  bool ble = false;
  bool unlock = false;
  bool start = false;
  final num Speed;
  num volt; // Mutable to allow updates.
  final num temp;
  final num hum;
  final num Lat;
  final num Lon;
  final num motion;
  num sta; // Mutable as it's derived from volt.
  final num light;
  final String ver;
  final num rel2;
  final int backlight;
  bool virtual;
  int rssi; // New field for RSSI

  /// Constructor for DeviceStatus.
  /// [sta] is derived from [volt] to ensure consistency.
  DeviceStatus(
    this.Speed,
    this.volt,
    this.temp,
    this.hum,
    this.Lat,
    this.Lon,
    this.motion,
    num sta, // Initial value; will be overridden.
    this.light,
    this.ver,
    this.rel2,
    this.backlight, {
    this.virtual = false,
    this.rssi = -100, // Default value for RSSI
  })  : sta = volt >= 13.5 ? 1 : 0,
        start = volt >= 13.5;

  factory DeviceStatus.initial() {
    return DeviceStatus(
      0, // Speed
      0, // volt
      0, // temp
      0, // hum
      0, // Lat
      0, // Lon
      0, // motion
      0, // sta (derived)
      0, // light
      '1.0.0', // ver
      0, // rel2
      0, // backlight
      virtual: true, // Set virtual to true as per AppProvider's initialization
      rssi: -100, // Default RSSI value
    );
  }

  /// Factory constructor to create a DeviceStatus instance from a map.
  factory DeviceStatus.fromMap(Map<String, dynamic> map) {
    num parsedLat = num.tryParse((map['Lat']?.toString() ?? '0.0')
            .replaceAll(RegExp(r'[^0-9.-]'), '')) ??
        0.0;
    num parsedLon = num.tryParse((map['Lon']?.toString() ?? '0.0')
            .replaceAll(RegExp(r'[^0-9.-]'), '')) ??
        0.0;
    String version = map['ver']?.toString() ?? '';

    return DeviceStatus(
      num.tryParse(map['Speed']?.toString() ?? '0.0') ?? 0.0,
      num.tryParse(map['volt']?.toString() ?? '0.0') ?? 0.0,
      num.tryParse(map['temp']?.toString() ?? '0.0') ?? 0.0,
      num.tryParse(map['hum']?.toString() ?? '0.0') ?? 0.0,
      parsedLat,
      parsedLon,
      num.tryParse(map['motion']?.toString() ?? '0.0') ?? 0.0,
      num.tryParse(map['sta']?.toString() ?? '0') ?? 0, // Derived from volt
      num.tryParse(map['light']?.toString() ?? '0.0') ?? 0.0,
      version,
      num.tryParse(map['rel2']?.toString() ?? '0.0') ?? 0.0,
      map['backlight'] ?? 0,
      virtual: map['virtual'] ?? false,
      rssi: int.tryParse(map['rssi']?.toString() ?? '-100') ?? -100,
    );
  }

  /// Setter for the [unlock] property.
  set isUnlock(bool value) {
    unlock = value;
  }

  /// Getter for the [isOnline] property.
  bool get isOnline => online;

  /// Setter for the [isOnline] property.
  set isOnline(bool value) {
    online = value;
  }

  @override
  String toString() {
    return '{volt: \$volt, temp:\$temp, light:\$light, online:\$online, unlock:\$unlock, rssi:\$rssi}';
  }

  Map<String, dynamic> toJson() {
    return {
      'lon': Lon,
      'lat': Lat,
      'ver': ver,
      'rel2': rel2,
      'hum': hum,
      'sta': sta,
      'volt': volt,
      'speed': Speed,
      'temp': temp,
      'motion': motion,
      'light': light,
      'rssi': rssi, // Include RSSI in JSON
    };
  }
}

/// Notifier class to handle changes in DeviceStatus.
class DeviceStatusNotifier extends ChangeNotifier {
  late DeviceStatus _status;

  /// Sets the device status and notifies listeners.
  set status(DeviceStatus newStatus) {
    _status = newStatus;
    notifyListeners();
  }

  /// Gets the current device status.
  DeviceStatus get status => _status;
}
