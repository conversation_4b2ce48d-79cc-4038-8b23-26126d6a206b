import 'package:aslaa/main/util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:provider/provider.dart';
import '../manager/ble_manager.dart';
import '../models/device.dart';
import '../models/user.dart';
import '../providers/app_provider.dart';
import 'device_details_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key, required this.title}) : super(key: key);

  final String title;

  @override
  State<HomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<HomePage> {
  List<ScanResult>? scanResults;
  List<int> openedRows = [];
  bool isScaning = false;
// 通用白色背景阴影
  final kBoxShadowStyle = <BoxShadow>[
    const BoxShadow(
        color: Color.fromRGBO(12, 0, 51, 0.05),
        offset: Offset(0, 4),
        blurRadius: 8)
  ];

  @override
  void initState() {
    // TODO: implement initState

    super.initState();
  }

  void scanResultHandler(List<ScanResult>? results) {
    // print('results ====> $results');

    for (ScanResult r in results!) {
      // print('${r.device.name} found! rssi: ${r.rssi}');
    }

    setState(() {
      scanResults = results;
    });
  }

  renderRow(i) {
    ScanResult scanResult = scanResults![i];
    AppProvider authProvider = Provider.of<AppProvider>(context, listen: false);
    User? user = authProvider.authClient;
    Device? _selectedDevice =
        user?.devices?.where((e) => e.isDefault == true).first;
    return InkWell(
        onTap: () {
          setState(() {
            if (openedRows.contains(i)) {
              openedRows.remove(i);
            } else {
              openedRows.add(i);
            }
          });
        },
        child: Container(
          padding:
              const EdgeInsets.only(left: 15, right: 15, top: 20, bottom: 15),
          margin: const EdgeInsets.only(left: 15, right: 15, top: 15),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.all(Radius.circular(10)),
              boxShadow: kBoxShadowStyle),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    Util.safeStr(
                        scanResult.device.name,
                        Util.safeStr(
                            scanResult.advertisementData.localName, "N/A")),
                    style: const TextStyle(
                        fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  const Icon(Icons.star),
                  const Spacer(),
                  if (scanResult.advertisementData.connectable)
                    (GestureDetector(
                      onTap: () {
                        Navigator.of(context)
                            .push(MaterialPageRoute(builder: (context) {
                          // scanResult.device.connect();
                          return DeviceDetailsPage(result: scanResult);
                        }));
                      },
                      child: Container(
                        padding: const EdgeInsets.only(
                            top: 7, bottom: 7, left: 15, right: 15),
                        decoration: const BoxDecoration(
                            color: Colors.blue,
                            borderRadius: BorderRadius.all(Radius.circular(5))),
                        child: const Text(
                          "Connect",
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                      ),
                    ))
                ],
              ),
              const SizedBox(
                height: 5,
              ),
              Text(
                "${scanResult.device.id}",
                style: const TextStyle(fontSize: 12),
              ),
              const SizedBox(
                height: 15,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(Icons.bluetooth_connected_outlined),
                      const SizedBox(
                        height: 5,
                      ),
                      Text(scanResult.advertisementData.connectable
                          ? "Connectable"
                          : "Non-Connectable"),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(Icons.wifi),
                      const SizedBox(
                        height: 5,
                      ),
                      Text("${scanResult.rssi}"),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: const [
                      Icon(Icons.wifi_tethering),
                      SizedBox(
                        height: 5,
                      ),
                      Text("Unspecified"),
                    ],
                  )
                ],
              ),
              if (openedRows.contains(i)) (setupMoreInfoWidget(scanResult)),
              if (BleManager.instance.checkeDevice(
                      _selectedDevice?.deviceNumber ?? "",
                      scanResult.advertisementData.manufacturerData) ==
                  true)
                (Row(
                  children: [
                    Spacer(),
                    Container(
                      padding: const EdgeInsets.only(
                          top: 7, bottom: 7, left: 15, right: 15),
                      margin: const EdgeInsets.only(top: 16),
                      decoration: const BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.all(Radius.circular(5))),
                      child: Text(
                        "识别到设备：${_selectedDevice?.deviceNumber}",
                        style: TextStyle(color: Colors.white, fontSize: 14),
                      ),
                    )
                  ],
                )),
              GestureDetector(
                onTap: () {
                  Clipboard.setData(ClipboardData(text: scanResult.toString()));
                },
                child: Row(
                  children: [
                    Spacer(),
                    Container(
                      padding: const EdgeInsets.only(
                          top: 7, bottom: 7, left: 15, right: 15),
                      margin: const EdgeInsets.only(top: 16),
                      decoration: const BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.all(Radius.circular(5))),
                      child: const Text(
                        "复制信息",
                        style: TextStyle(color: Colors.white, fontSize: 14),
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ));
  }

  setupMoreInfoWidget(ScanResult scanResult) {
    return Column(
      children: [
        if (!Util.isEmpty(scanResult.advertisementData.txPowerLevel))
          (Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              const Divider(),
              const SizedBox(
                height: 5,
              ),
              const Text(
                "TX Power Level",
                style: TextStyle(color: Color(0xFF999999)),
              ),
              const SizedBox(
                height: 5,
              ),
              Text("${scanResult.advertisementData.txPowerLevel}"),
            ],
          )),
        if (!Util.isEmpty(scanResult.advertisementData.localName))
          (Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              const Divider(),
              const SizedBox(
                height: 5,
              ),
              const Text(
                "Complete Local Name",
                style: TextStyle(color: Color(0xFF999999)),
              ),
              const SizedBox(
                height: 5,
              ),
              Text(scanResult.advertisementData.localName),
            ],
          )),
        if (!Util.isEmpty(scanResult.advertisementData.manufacturerData) &&
            scanResult.advertisementData.manufacturerData.isNotEmpty)
          (Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              const Divider(),
              const SizedBox(
                height: 5,
              ),
              const Text(
                "Manufacturer Specific Data",
                style: TextStyle(color: Color(0xFF999999)),
              ),
              const SizedBox(
                height: 5,
              ),
              Text(BleManager.instance.getNiceManufacturerData(
                  scanResult.advertisementData.manufacturerData)),
            ],
          )),
        if (!Util.isEmpty(scanResult.advertisementData.serviceUuids) &&
            scanResult.advertisementData.serviceUuids.isNotEmpty)
          (Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              const Divider(),
              const SizedBox(
                height: 5,
              ),
              const Text(
                "Service UUIDs",
                style: TextStyle(color: Color(0xFF999999)),
              ),
              const SizedBox(
                height: 5,
              ),
              Text(scanResult.advertisementData.serviceUuids
                  .join(', ')
                  .toUpperCase()),
            ],
          )),
        if (!Util.isEmpty(scanResult.advertisementData.serviceData) &&
            scanResult.advertisementData.serviceData.isNotEmpty)
          (Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              const Divider(),
              const SizedBox(
                height: 5,
              ),
              const Text(
                "Service Data",
                style: TextStyle(color: Color(0xFF999999)),
              ),
              const SizedBox(
                height: 5,
              ),
              Text(BleManager.instance.getNiceServiceData(scanResult
                  .advertisementData.serviceData
                  .cast<Guid, List<int>>())),
            ],
          ))
      ],
    );
  }

  Widget setupNoDataView() {
    return Center(
      child: Container(
          height: 300,
          padding: EdgeInsets.all(20),
          alignment: Alignment.center,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Container(
                  width: 180,
                  height: 180,
                  child: Image.asset(
                    "assets/images/driver-license.png",
                    fit: BoxFit.fill,
                  )),
              const SizedBox(
                height: 20,
              ),
              const Text("Looking for nearby devices...",
                  style: TextStyle(color: Color(0xFF666666), fontSize: 18)),
            ],
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // Here we take the value from the MyHomePage object that was created by
        // the App.build method, and use it to set our appbar title.
        title: Text(widget.title),
      ),
      body: Util.isEmpty(scanResults)
          ? setupNoDataView()
          : Container(
              color: const Color(0xFFFAFAFA),
              child: ListView.builder(
                padding: const EdgeInsets.only(bottom: 10),
                itemCount: Util.safeStr(scanResults, []).length,
                itemBuilder: (context, i) => renderRow(i),
              ),
            ),
      bottomNavigationBar: SizedBox(
          height: 70 + MediaQuery.of(context).padding.bottom,
          child: BottomAppBar(
            elevation: 15,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                GestureDetector(
                  onTap: () {
                    // startBle();
                    if (isScaning) {
                      BleManager.instance.stopScan();
                      setState(() {
                        isScaning = false;
                      });
                    } else {
                      BleManager.instance.startScan(scanResultHandler);
                      setState(() {
                        isScaning = true;
                      });
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.only(left: 20, right: 20),
                    height: 40,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        color: isScaning ? Colors.red : Colors.blue,
                        borderRadius:
                            const BorderRadius.all(Radius.circular(5.0))),
                    child: Text(
                      isScaning ? "Stop Scaning" : "Start Scaning",
                      style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                )
              ],
            ),
          )), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}
