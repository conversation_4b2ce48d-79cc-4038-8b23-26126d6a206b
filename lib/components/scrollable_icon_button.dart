import 'package:aslaa/mqtt/mqtt_websocket.dart';
import 'package:flutter/material.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/flutter_flow/flutter_flow_theme.dart';
import 'package:aslaa/models/device.dart';
import 'package:flutter/services.dart'; // Import for HapticFeedback

class ScrollableIconButton extends StatefulWidget {
  final User user;
  final DeviceStatus deviceStatus;
  final Future<bool> Function(User user, String command) sendDeviceCommand;
  final MqttHandler? mqtthandler; // Nullable

  ScrollableIconButton({
    Key? key,
    required this.user,
    required this.sendDeviceCommand,
    required this.deviceStatus,
    this.mqtthandler,
  }) : super(key: key);

  @override
  _ScrollableIconButtonState createState() => _ScrollableIconButtonState();
}

class _ScrollableIconButtonState extends State<ScrollableIconButton>
    with TickerProviderStateMixin {
  AnimationController? _controller;
  late AnimationController _successAnimationController;

  int _duration = 2; // Duration in seconds for holding the button
  IconData? currentIcon;
  final IconData defaultIcon = Icons.power_settings_new;
  final IconData upIcon = Icons.flip_camera_android;
  final IconData downIcon = Icons.flip;
  bool isPressed = false;
  bool showSuccess = false;

  @override
  void initState() {
    super.initState();
    currentIcon = defaultIcon;

    // Initialize the main animation controller
    _controller = AnimationController(
      vsync: this,
      duration: Duration(seconds: _duration),
    );

    // Initialize the success animation controller
    _successAnimationController = AnimationController(
      vsync: this,
      duration:
          Duration(milliseconds: 500), // Short duration for success animation
    );

    // Add a status listener to the main controller
    _controller!.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // Only execute the command if the animation is fully completed
        _executeCommandAndReset();
      }
    });
  }

  void _executeCommandAndReset() {
    String command = _constructCommand();

    _sendCommand(command).then((_) {
      // After sending the command, trigger the success animation
      setState(() => showSuccess = true);
      _successAnimationController.forward().then((_) {
        Future.delayed(Duration(seconds: 1), () {
          setState(() => showSuccess = false);
          _successAnimationController.reset();
        });
      });
    }).catchError((error) {
      print('Error sending command: $error');
    });
  }

  String _constructCommand() {
    if (currentIcon == upIcon) {
      return 'as';
    } else if (currentIcon == downIcon) {
      return 'mirror';
    } else {
      String? uix = widget.user.device?.uix;
      if (uix != null) {
        if (uix.toLowerCase().contains('chip')) {
          return widget.deviceStatus.sta == 1 ? 'off1' : 'on1';
        } else if (uix.toLowerCase().contains('car2')) {
          return widget.deviceStatus.sta == 1 ? 'unt' : 'asa';
        }
        return widget.deviceStatus.sta == 1 ? 'untar' : 'as';
      }
      return 'defaultCommand'; // Fallback command
    }
  }

  Future<void> _sendCommand(String command) async {
    print('Constructed command: $command');

    // Send the command via the provided function
    await widget.sendDeviceCommand(widget.user, command);

    setState(() {
      currentIcon = defaultIcon; // Reset the icon after command execution
    });

    _controller!.value = 0.0; // Reset the animation controller
  }

  void _onButtonPressed() {
    HapticFeedback.lightImpact(); // Add HapticFeedback
    print('Button Pressed');
    setState(() => isPressed = true);
    _controller!.forward(from: 0);
    print('Animation Started: ${_controller!.isAnimating}');
  }

  void _onButtonReleased() {
    print('Button Released');
    if (_controller!.isAnimating) {
      _controller!.stop();
      print('Animation Stopped: ${_controller!.isAnimating}');
      setState(() {
        if (_controller!.value < 1.0) {
          print('Command Cancelled');
          currentIcon = defaultIcon;
          _controller!.value = 0.0;
        }
      });
    }
  }

  void _onScroll(DragUpdateDetails details) {
    setState(() {
      if (details.primaryDelta! > 0) {
        currentIcon = downIcon; // Scrolled down
      } else if (details.primaryDelta! < 0) {
        currentIcon = upIcon; // Scrolled up
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    Color buttonColor = showSuccess ? Colors.green : Color(0x6939D2C0);

    return GestureDetector(
      onVerticalDragUpdate: _onScroll,
      onLongPress: _onButtonPressed,
      onLongPressUp: _onButtonReleased,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: buttonColor,
          borderRadius: BorderRadius.circular(40),
          border: Border.all(
            color: FlutterFlowTheme.of(context).secondaryColor,
            width: 1,
          ),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            Icon(
              currentIcon!,
              color: FlutterFlowTheme.of(context).primaryText,
              size: 30,
            ),
            Positioned.fill(
              child: AnimatedBuilder(
                animation: _controller!,
                builder: (context, child) {
                  return CircularProgressIndicator(
                    value: _controller!.value,
                    backgroundColor: buttonColor,
                    valueColor: AlwaysStoppedAnimation<Color>(
                        _controller!.isAnimating ? Colors.green : buttonColor),
                    strokeWidth: 6.0,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller!.dispose();
    _successAnimationController.dispose();
    super.dispose();
  }
}
