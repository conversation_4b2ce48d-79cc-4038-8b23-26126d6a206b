// lib/widgets/right_side_buttons.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:aslaa/components/weather_display.dart';
import 'package:aslaa/components/temperature_display.dart';
import 'package:aslaa/components/gps_status_indicator.dart';
import 'package:aslaa/components/recording_button.dart';
import 'package:aslaa/components/bluetooth_button.dart';
import 'package:aslaa/components/version_button.dart';
import 'package:aslaa/providers/app_provider.dart';

class RightSideButtons extends StatefulWidget {
  final bool isRecording;
  final bool isBluetoothEnabled;
  final Function onRecordingPressed;
  final Function onBluetoothPressed;

  const RightSideButtons({
    Key? key,
    required this.isRecording,
    required this.isBluetoothEnabled,
    required this.onRecordingPressed,
    required this.onBluetoothPressed,
  }) : super(key: key);

  @override
  _RightSideButtonsState createState() => _RightSideButtonsState();
}

class _RightSideButtonsState extends State<RightSideButtons> {
  final weatherTooltipKey = GlobalKey<TooltipState>();
  final temperatureTooltipKey = GlobalKey<TooltipState>();
  final gpsTooltipKey = GlobalKey<TooltipState>();
  final recordingTooltipKey = GlobalKey<TooltipState>();
  final bluetoothTooltipKey = GlobalKey<TooltipState>();
  final versionTooltipKey = GlobalKey<TooltipState>();

  @override
  Widget build(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context);
    final weatherData = appProvider.weatherData;
    final ds = appProvider.ds;

    return Align(
      alignment: AlignmentDirectional(1, 0),
      child: Padding(
        padding: EdgeInsetsDirectional.fromSTEB(0, 0, 12, 0),
        child: Container(
          width: 85,
          child: Padding(
            padding: EdgeInsetsDirectional.fromSTEB(0, 0, 12, 0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    weatherTooltipKey.currentState?.ensureTooltipVisible();
                  },
                  child: Tooltip(
                    key: weatherTooltipKey,
                    message: 'Машины гаднах температур',
                    child: WeatherDisplay(
                      temperature:
                          weatherData?["current"]["temp_c"]?.toString() ??
                              "N/A",
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    temperatureTooltipKey.currentState?.ensureTooltipVisible();
                  },
                  child: Tooltip(
                    key: temperatureTooltipKey,
                    message: 'машин дахь температур',
                    child: TemperatureDisplay(
                      initialTemp: ds.temp - 22,
                      targetTemp: ds.temp - 20,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    gpsTooltipKey.currentState?.ensureTooltipVisible();
                  },
                  child: Tooltip(
                    key: gpsTooltipKey,
                    message: 'GPS сүлжээ',
                    child: GpsStatusIndicator(
                      latitude: ds.Lat.toDouble(),
                      longitude: ds.Lon.toDouble(),
                      isVirtual: ds.virtual,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    recordingTooltipKey.currentState?.ensureTooltipVisible();
                  },
                  child: Tooltip(
                    key: recordingTooltipKey,
                    message: 'Лицензийн дуусах хугацаа өдрөөр',
                    child: RecordingButton(
                      isRecording: widget.isRecording,
                      onPressed: () => widget.onRecordingPressed(),
                      user: appProvider.user,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    bluetoothTooltipKey.currentState?.ensureTooltipVisible();
                  },
                  child: Tooltip(
                    key: bluetoothTooltipKey,
                    message: 'Bluetooth',
                    child: BluetoothButton(
                      isEnabled: widget.isBluetoothEnabled,
                      onPressed: () => widget.onBluetoothPressed(),
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    versionTooltipKey.currentState?.ensureTooltipVisible();
                  },
                  child: Tooltip(
                    key: versionTooltipKey,
                    message:
                        'Машин дахь асаагч төхөөрөмжийн проргамын хувилбар',
                    child: VersionButton(
                      version: ds.ver.toString(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
