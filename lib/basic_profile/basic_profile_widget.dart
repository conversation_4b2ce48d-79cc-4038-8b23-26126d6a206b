import 'dart:convert';
import 'dart:io';
import 'package:aslaa/constant.dart';
import 'package:aslaa/models/device.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import '../flutter_flow/flutter_flow_drop_down.dart';
import '../flutter_flow/flutter_flow_icon_button.dart';
import '../flutter_flow/flutter_flow_language_selector.dart';
import '../flutter_flow/flutter_flow_radio_button.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_util.dart';
import '../flutter_flow/flutter_flow_widgets.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:http/http.dart' as http;

class BasicProfileWidget extends StatefulWidget {
  const BasicProfileWidget({Key? key}) : super(key: key);

  @override
  _BasicProfileWidgetState createState() => _BasicProfileWidgetState();
}

class _BasicProfileWidgetState extends State<BasicProfileWidget> {
  String? dropDownValue;
  String? radioButtonValue3;
  TextEditingController? deviceNameController;
  TextEditingController? deviceNumberController;
  String radioButtonValue4 = '4G';
  bool? switchValue;
  String? radioButtonValue1;
  TextEditingController? addressController;
  TextEditingController? citizenController;
  TextEditingController? fullNameController;
  String? radioButtonValue2;
  TextEditingController? bankAccountController;
  TextEditingController? bankNameController;
  TextEditingController? amountController;
  TextEditingController? confirmPasswordController;
  late bool confirmPasswordVisibility;
  TextEditingController? oldPasswordController;
  late bool oldPasswordVisibility;
  TextEditingController? passwordController;
  late bool passwordVisibility;
  final _unfocusNode = FocusNode();
  final scaffoldKey = GlobalKey<ScaffoldState>();
  final formKey = GlobalKey<FormState>();
  String deviceMode = "new";
  Device? selectedDevice;

  Future _launchUrl(_url) async {
    try {
      if (!await launchUrl(Uri.parse(_url))) {
        throw Exception('could not launch url : $_url');
      }
    } catch (err) {}
  }

  Uint8List convertBase64Image(String base64String) {
    return Base64Decoder().convert(base64String.split(',').last);
  }

  // deposit request
  Future _depositRequest(AppProvider provider) async {
    String qrcode = "";
    List<dynamic>? urls;

    var amount = num.tryParse((amountController?.text)!);
    if (amount != null && amount > 1000) {
      try {
        SharedPreferences _prefs = await SharedPreferences.getInstance();
        String token = _prefs.getString('token') ?? provider.user!.token;
        Map<String, String> headers = {
          'Content-Type': 'application/json; charset=UTF-8',
          'Authorization': 'Bearer $token',
        };
        Map<String, dynamic> body = {
          "totalCost": amount,
          "page": 'balance',
        };
        var response = await http.post(
            Uri.parse('$API_HOST/api/device/extend-balance'),
            headers: headers,
            body: jsonEncode(body));
        if (response.statusCode == 200) {
          Map<String, dynamic> json = jsonDecode(response.body);
          if (json['data'] != null) {
            Map<String, dynamic> invoice = json['data']["bankList"];
            urls = invoice['urls'];
            qrcode = invoice['qr_image'];
            debugPrint('$urls');
            showDialog(
                context: context,
                builder: ((context) => Dialog.fullscreen(
                        child: SingleChildScrollView(
                      child: Column(
                        children: [
                          Padding(
                            padding:
                                EdgeInsetsDirectional.fromSTEB(24, 24, 0, 24),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.memory(
                                  convertBase64Image(qrcode),
                                  gaplessPlayback: true,
                                  width: 180,
                                  height: 180,
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding:
                                EdgeInsetsDirectional.fromSTEB(24, 24, 24, 0),
                            child: Column(
                                children: urls!
                                    .map((e) => GestureDetector(
                                          child: Row(children: [
                                            Image.network(
                                              e['logo'],
                                              width: 24,
                                            ),
                                            Padding(
                                              padding: EdgeInsetsDirectional
                                                  .fromSTEB(4, 0, 0, 0),
                                              child: Text(
                                                e['name'],
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyText2
                                                        .override(
                                                          fontFamily: 'Roboto',
                                                          fontSize: 24,
                                                          fontWeight:
                                                              FontWeight.normal,
                                                        ),
                                              ),
                                            )
                                          ]),
                                          onTap: () async {
                                            _launchUrl(e['link']);
                                          },
                                        ))
                                    .toList()),
                          ),
                          Padding(
                            padding:
                                EdgeInsetsDirectional.fromSTEB(24, 24, 24, 24),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                FFButtonWidget(
                                  onPressed: () async {
                                    provider.reload();
                                    Navigator.pop(context);
                                  },
                                  text: FFLocalizations.of(context).getText(
                                    'b3bahb30' /* Close */,
                                  ),
                                  options: FFButtonOptions(
                                    width: 130,
                                    height: 40,
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryColor,
                                    textStyle: FlutterFlowTheme.of(context)
                                        .subtitle2
                                        .override(
                                          fontFamily: 'Roboto',
                                          color: FlutterFlowTheme.of(context)
                                              .primaryText,
                                          fontWeight: FontWeight.w500,
                                        ),
                                    borderSide: BorderSide(
                                      color: Colors.transparent,
                                      width: 1,
                                    ),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ))));
          }
        } else {
          showAnimatedSnackbar(
              context,
              "Internal Server Error - ${response.statusCode}",
              "Failed",
              ContentType.failure);
        }
      } catch (err) {
        showAnimatedSnackbar(context, "Please check your network connection",
            "Failed", ContentType.failure);
      }
    }
  }

  // withdraw request
  Future _requestWithdraw(AppProvider provider) async {
    var amount = num.tryParse((amountController?.text)!);
    if (amount != null &&
        amount > 1000 &&
        amount <= (provider.user?.balance)!) {
      try {
        SharedPreferences _prefs = await SharedPreferences.getInstance();
        String token = _prefs.getString('token') ?? provider.user!.token;
        Map<String, String> headers = {
          'Content-Type': 'application/json; charset=UTF-8',
          'Authorization': 'Bearer $token',
        };
        Map<String, dynamic> body = {
          "payAmount": amount,
        };
        var response = await http.post(
            Uri.parse('$API_HOST/api/auth/request-withdraw'),
            headers: headers,
            body: jsonEncode(body));
        if (response.statusCode == 200) {
          Map<String, dynamic> json = jsonDecode(response.body);
          if (json['success']) {
            showAnimatedSnackbar(context, "Submitted your withdraw request",
                "Success", ContentType.success);
            provider.reload();
          } else {
            showAnimatedSnackbar(
                context,
                json['message'] ?? 'Whoops! please try again',
                "Warning",
                ContentType.warning);
          }
        } else {
          showAnimatedSnackbar(
              context,
              "Internal Server Error - ${response.statusCode}",
              "Failed",
              ContentType.failure);
        }
      } catch (err) {
        showAnimatedSnackbar(context, "Please check your network connection",
            "Failed", ContentType.failure);
      }
    } else {
      showAnimatedSnackbar(
          context,
          "Withdraw amount can not less than 1000 or greater than user balance",
          "Failed",
          ContentType.failure);
    }
  }

  // save device infomation

  Future saveDevice(AppProvider authProvider) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      String token = _prefs.getString('token') ?? authProvider.user!.token;
      Map<String, String> headers = {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      };
      Map<String, dynamic> body = {
        "deviceName": deviceNameController!.text,
        "deviceNumber": deviceNumberController!.text,
        "uix": dropDownValue,
        "isDefault": switchValue!,
        "type": radioButtonValue4.toLowerCase(),
        // "rentable": 0,
        "phoneNumber": authProvider.user?.phoneNumber,
        "_id": deviceMode == 'edit' ? selectedDevice?.id : '',
      };
      var response = await http.post(Uri.parse('$API_HOST/api/device/register'),
          headers: headers, body: jsonEncode(body));
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['success']) {
          showAnimatedSnackbar(context, "Saved Device Information Success",
              "Success", ContentType.success);
          authProvider.reload();
        } else {
          showAnimatedSnackbar(
              context,
              json['nessage'] ?? 'Please check your device details',
              "Warning",
              ContentType.warning);
        }
      } else {
        showAnimatedSnackbar(
            context,
            "Internal Server Error - ${response.statusCode}",
            "Failed",
            ContentType.failure);
      }
    } catch (err) {
      showAnimatedSnackbar(context, "Please check your network connection",
          "Failed", ContentType.failure);
    }
  }

  Future deleteDevice(AppProvider authProvider) async {
    try {
      if (selectedDevice == null) {
        return;
      }
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      String token = _prefs.getString('token') ?? authProvider.user!.token;
      Map<String, String> headers = {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      };

      var response = await http.delete(
        Uri.parse('$API_HOST/api/device/delete/${selectedDevice?.id}'),
        headers: headers,
      );
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        if (json['success']) {
          showAnimatedSnackbar(
              context, "Deleted Devices", "Success", ContentType.success);
          authProvider.reload();
        } else {
          showAnimatedSnackbar(
              context,
              json['nessage'] ?? 'Please check your device details',
              "Warning",
              ContentType.warning);
        }
      } else {
        showAnimatedSnackbar(
            context,
            "Internal Server Error - ${response.statusCode}",
            "Failed",
            ContentType.failure);
      }
    } catch (err) {
      showAnimatedSnackbar(context, "Please check your network connection",
          "Failed", ContentType.failure);
    }
  }
  // save bank information

  Future saveBankInfomation(AppProvider authProvider) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      String token = _prefs.getString('token') ?? authProvider.user!.token;
      Map<String, String> headers = {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      };
      Map<String, String> body = {
        "bankAccount": bankAccountController!.text,
        "bankName": bankNameController!.text,
      };
      var response = await http.post(Uri.parse('$API_HOST/api/auth/set-bank'),
          headers: headers, body: jsonEncode(body));
      if (response.statusCode == 200) {
        showAnimatedSnackbar(context, "Saved Bank Account Success", "Success",
            ContentType.success);

        // update user's wallet
        authProvider.updateWallet(
            bankName: bankNameController!.text,
            bankAccount: bankAccountController!.text);
      } else {
        showAnimatedSnackbar(
            context,
            "Internal Server Error - ${response.statusCode}",
            "Failed",
            ContentType.failure);
      }
    } catch (err) {
      showAnimatedSnackbar(context, "Please check your network connection",
          "Failed", ContentType.failure);
    }
  }

  // update security information
  Future updatePassword(AppProvider authProvider) async {
    try {
      User user = authProvider.user!;
      if (confirmPasswordController?.text != passwordController?.text) {
        showAnimatedSnackbar(
            context,
            "Please check your password and confirm password",
            "Failed",
            ContentType.failure);
        return;
      }
      if (oldPasswordController!.text != user.password) {
        showAnimatedSnackbar(context, "Your previous password is not matched",
            "Failed", ContentType.failure);
        return;
      }

      SharedPreferences _prefs = await SharedPreferences.getInstance();
      String token = _prefs.getString('token') ?? user.token;
      Map<String, String> headers = {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      };
      Map<String, String> body = {
        "oldPinCode": oldPasswordController!.text,
        "newPinCode": passwordController!.text,
        "phoneNumber": user.phoneNumber,
        "username": user.username,
      };

      var response = await http.post(
          Uri.parse('$API_HOST/api/auth/set-pincode'),
          headers: headers,
          body: jsonEncode(body));
      if (response.statusCode == 200) {
        showAnimatedSnackbar(
            context, "Saved Security Success", "Success", ContentType.success);

        // update user's password
        authProvider.updatePassword(
          password: passwordController!.text,
        );
      } else {
        showAnimatedSnackbar(
            context,
            "Internal Server Error - ${response.statusCode}",
            "Failed",
            ContentType.failure);
      }
    } catch (err) {
      showAnimatedSnackbar(context, "Please check your network connection",
          "Failed", ContentType.failure);
    }
  }

  // save driver license file
  Future saveProfile(AppProvider authProvider) async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    String token = _prefs.getString('token') ?? authProvider.user!.token;
    if (image != null) {
      try {
        debugPrint(image!.path);
        Map<String, String> headers = {
          'Content-Type': 'multipart/form-data',
          'Authorization': 'Bearer $token',
        };
        Map<String, String> body = {
          "username": fullNameController!.text,
          "address": addressController!.text,
          "description": citizenController!.text,
        };
        var request = http.MultipartRequest(
            'POST', Uri.parse('$API_HOST/api/device/set-driver-profile'))
          ..fields.addAll(body)
          ..headers.addAll(headers)
          ..files.add(await http.MultipartFile.fromPath(
              'driverLicenseFile', image!.path));
        var response = await request.send();

        if (response.statusCode == 200) {
          showAnimatedSnackbar(
              context, "Saved Profile Success", "Success", ContentType.success);
          authProvider.reload();
        } else {
          showAnimatedSnackbar(
              context,
              "Internal Server Error - ${response.statusCode}",
              "Failed",
              ContentType.failure);
        }
      } catch (e) {
        debugPrint('${e.toString()}');
      }
    } else {
      showAnimatedSnackbar(context, "Please select your license file", "Failed",
          ContentType.failure);
    }
  }

  File? image;

  void _pickImage({required ImageSource imageSource}) async {
    XFile? pickedFile = await ImagePicker().pickImage(source: imageSource);
    if (pickedFile != null) {
      setState(() {
        image = File(pickedFile.path);
      });
    }
  }

  @override
  void initState() {
    super.initState();
    addressController = TextEditingController();
    citizenController = TextEditingController();
    fullNameController = TextEditingController();
    amountController = TextEditingController();
    bankAccountController = TextEditingController();
    bankNameController = TextEditingController();
    confirmPasswordController = TextEditingController();
    confirmPasswordVisibility = false;
    oldPasswordController = TextEditingController();
    oldPasswordVisibility = false;
    passwordController = TextEditingController();
    passwordVisibility = false;
    deviceNameController = TextEditingController();
    deviceNumberController = TextEditingController();
  }

  @override
  void dispose() {
    _unfocusNode.dispose();
    addressController?.dispose();
    citizenController?.dispose();
    fullNameController?.dispose();
    amountController?.dispose();
    bankAccountController?.dispose();
    bankNameController?.dispose();
    confirmPasswordController?.dispose();
    oldPasswordController?.dispose();
    passwordController?.dispose();
    deviceNameController?.dispose();
    deviceNumberController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    AppProvider authProvider = Provider.of<AppProvider>(context);
    User? user = authProvider.authClient;
    addressController?.text = user!.address;
    fullNameController?.text = user!.username;
    citizenController?.text = user!.description;
    bankAccountController?.text = user!.wallet.bankAccount;
    bankNameController?.text = user!.wallet.bankName;

    List<String>? devices = user?.devices
        ?.map((e) => e.deviceName != '' ? e.deviceName : e.deviceNumber)
        .toList();
    if (devices == null) {
      devices = [FFLocalizations.of(context).getText('eihwbyx6')].toList();
    } else
      devices.insert(0, FFLocalizations.of(context).getText('eihwbyx6'));

    return Scaffold(
      key: scaffoldKey,
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      appBar: AppBar(
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        automaticallyImplyLeading: false,
        title: Text(
          FFLocalizations.of(context).getText(
            'zruwhuzw' /* Personal Infomation */,
          ),
          style: FlutterFlowTheme.of(context).title1,
        ),
        actions: [],
        centerTitle: false,
        elevation: 0,
      ),
      body: SafeArea(
        child: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(_unfocusNode),
          child: Stack(
            children: [
              DefaultTabController(
                length: 4,
                initialIndex: 0,
                child: Column(
                  children: [
                    TabBar(
                      labelColor: FlutterFlowTheme.of(context).primaryText,
                      labelStyle: FlutterFlowTheme.of(context).bodyText1,
                      indicatorColor:
                          FlutterFlowTheme.of(context).secondaryColor,
                      tabs: [
                        Tab(
                          text: FFLocalizations.of(context).getText(
                            '1ipyytif' /* Profile */,
                          ),
                        ),
                        Tab(
                          text: FFLocalizations.of(context).getText(
                            'mc4mpuxk' /* Wallet */,
                          ),
                        ),
                        Tab(
                          text: FFLocalizations.of(context).getText(
                            'bxr5e3d1' /* Security */,
                          ),
                        ),
                        Tab(
                          text: FFLocalizations.of(context).getText(
                            'a03hzgid' /* Devices */,
                          ),
                        ),
                      ],
                    ),
                    Expanded(
                      child: TabBarView(
                        children: [
                          Container(
                            width: MediaQuery.of(context).size.width,
                            child: Form(
                              key: formKey,
                              autovalidateMode: AutovalidateMode.disabled,
                              child: Padding(
                                padding:
                                    EdgeInsetsDirectional.fromSTEB(0, 24, 0, 0),
                                child: SingleChildScrollView(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Padding(
                                            padding:
                                                EdgeInsetsDirectional.fromSTEB(
                                                    24, 0, 0, 0),
                                            child: Text(
                                              FFLocalizations.of(context)
                                                  .getText(
                                                'vripvise' /* Application Setting */,
                                              ),
                                              style:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyText1,
                                            ),
                                          ),
                                        ],
                                      ),
                                      Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            30, 12, 24, 0),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            Padding(
                                              padding: EdgeInsetsDirectional
                                                  .fromSTEB(0, 0, 12, 0),
                                              child: Text(
                                                FFLocalizations.of(context)
                                                    .getText(
                                                  '0s1u1rgc' /* Language */,
                                                ),
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyText1
                                                        .override(
                                                          fontFamily: 'Roboto',
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .secondaryText,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                              ),
                                            ),
                                            FlutterFlowLanguageSelector(
                                              width: 200,
                                              backgroundColor:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryBackground,
                                              borderColor:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryBackground,
                                              dropdownColor:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryBackground,
                                              dropdownIconColor:
                                                  FlutterFlowTheme.of(context)
                                                      .primaryText,
                                              borderRadius: 8,
                                              textStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyText2
                                                      .override(
                                                        fontFamily: 'Roboto',
                                                        fontWeight:
                                                            FontWeight.normal,
                                                      ),
                                              hideFlags: true,
                                              flagSize: 24,
                                              flagTextGap: 8,
                                              currentLanguage:
                                                  FFLocalizations.of(context)
                                                      .languageCode,
                                              languages:
                                                  FFLocalizations.languages(),
                                              onChanged: (lang) =>
                                                  setAppLanguage(context, lang),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            30, 12, 24, 0),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            Padding(
                                              padding: EdgeInsetsDirectional
                                                  .fromSTEB(0, 0, 12, 0),
                                              child: Text(
                                                FFLocalizations.of(context)
                                                    .getText(
                                                  'r31da99p' /* UI Theme */,
                                                ),
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyText1
                                                        .override(
                                                          fontFamily: 'Roboto',
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .secondaryText,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                              ),
                                            ),
                                            FlutterFlowIconButton(
                                              borderColor: Colors.transparent,
                                              borderRadius: 20,
                                              borderWidth: 1,
                                              buttonSize: 40,
                                              icon: FaIcon(
                                                Theme.of(context).brightness ==
                                                        Brightness.light
                                                    ? FontAwesomeIcons.moon
                                                    : FontAwesomeIcons.sun,
                                                color:
                                                    FlutterFlowTheme.of(context)
                                                        .primaryText,
                                                size: 20,
                                              ),
                                              onPressed: () {
                                                final ThemeMode mode =
                                                    Theme.of(context)
                                                                .brightness ==
                                                            Brightness.light
                                                        ? ThemeMode.dark
                                                        : ThemeMode.light;
                                                setDarkModeSetting(
                                                    context, mode);
                                                print('IconButton pressed ...');
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            0, 20, 0, 0),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Padding(
                                              padding: EdgeInsetsDirectional
                                                  .fromSTEB(24, 0, 0, 0),
                                              child: Text(
                                                FFLocalizations.of(context)
                                                    .getText(
                                                  'oqpmjpr7' /* Basic Profile */,
                                                ),
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyText1,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0, 12, 0, 0),
                                          child: GestureDetector(
                                            onTap: () => {
                                              _pickImage(
                                                  imageSource:
                                                      ImageSource.gallery)
                                            },
                                            child: (user?.driverLicenseFile !=
                                                        "" &&
                                                    image == null)
                                                ? Image.network(
                                                    '$API_HOST/${user?.driverLicenseFile}',
                                                    width: 300,
                                                    height: 160,
                                                    fit: BoxFit.contain,
                                                  )
                                                : (image != null
                                                    ? Image.file(
                                                        image!,
                                                        width: 300,
                                                        height: 160,
                                                        fit: BoxFit.contain,
                                                      )
                                                    : Image.asset(
                                                        'assets/images/driver-license.png',
                                                        width: 300,
                                                        height: 160,
                                                        fit: BoxFit.contain,
                                                      )),
                                          )),
                                      Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            24, 24, 24, 0),
                                        child: Text(
                                          FFLocalizations.of(context).getText(
                                            'ax1mce7n' /* Upload a photo for us to easil... */,
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .bodyText1,
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            24, 12, 24, 0),
                                        child: TextFormField(
                                          controller: fullNameController,
                                          obscureText: false,
                                          decoration: InputDecoration(
                                            labelText:
                                                FFLocalizations.of(context)
                                                    .getText(
                                              'olzchg1q' /* Your Name */,
                                            ),
                                            hintText:
                                                FFLocalizations.of(context)
                                                    .getText(
                                              'davxz5o5' /* Input your full name */,
                                            ),
                                            hintStyle:
                                                FlutterFlowTheme.of(context)
                                                    .bodyText2,
                                            enabledBorder: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: Color(0x00000000),
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: Color(0x00000000),
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            errorBorder: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: Color(0x00000000),
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            focusedErrorBorder:
                                                OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: Color(0x00000000),
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            filled: true,
                                            fillColor:
                                                FlutterFlowTheme.of(context)
                                                    .secondaryBackground,
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .bodyText1
                                              .override(
                                                fontFamily: 'Roboto',
                                                fontWeight: FontWeight.normal,
                                              ),
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            24, 12, 24, 0),
                                        child: TextFormField(
                                          controller: citizenController,
                                          obscureText: false,
                                          decoration: InputDecoration(
                                            labelText:
                                                FFLocalizations.of(context)
                                                    .getText(
                                              'akyocoli' /* ID of Citizen */,
                                            ),
                                            hintText:
                                                FFLocalizations.of(context)
                                                    .getText(
                                              'w96lhppv' /* Input your ID */,
                                            ),
                                            hintStyle:
                                                FlutterFlowTheme.of(context)
                                                    .bodyText2,
                                            enabledBorder: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: Color(0x00000000),
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: Color(0x00000000),
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            errorBorder: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: Color(0x00000000),
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            focusedErrorBorder:
                                                OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: Color(0x00000000),
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            filled: true,
                                            fillColor:
                                                FlutterFlowTheme.of(context)
                                                    .secondaryBackground,
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .bodyText1
                                              .override(
                                                fontFamily: 'Roboto',
                                                fontWeight: FontWeight.normal,
                                              ),
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            24, 12, 24, 0),
                                        child: TextFormField(
                                          controller: addressController,
                                          obscureText: false,
                                          decoration: InputDecoration(
                                            labelText:
                                                FFLocalizations.of(context)
                                                    .getText(
                                              'otyc81f3' /* Address */,
                                            ),
                                            hintText:
                                                FFLocalizations.of(context)
                                                    .getText(
                                              'pkd1iehz' /* Input your address */,
                                            ),
                                            hintStyle:
                                                FlutterFlowTheme.of(context)
                                                    .bodyText2,
                                            enabledBorder: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: Color(0x00000000),
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: Color(0x00000000),
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            errorBorder: OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: Color(0x00000000),
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            focusedErrorBorder:
                                                OutlineInputBorder(
                                              borderSide: BorderSide(
                                                color: Color(0x00000000),
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            filled: true,
                                            fillColor:
                                                FlutterFlowTheme.of(context)
                                                    .secondaryBackground,
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .bodyText1
                                              .override(
                                                fontFamily: 'Roboto',
                                                fontWeight: FontWeight.normal,
                                              ),
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            24, 12, 24, 24),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Expanded(
                                              child: Padding(
                                                padding: EdgeInsetsDirectional
                                                    .fromSTEB(0, 0, 0, 0),
                                                child: FFButtonWidget(
                                                  onPressed: () async {
                                                    if (user?.role != 'guest')
                                                      await saveProfile(
                                                          authProvider);
                                                  },
                                                  text: FFLocalizations.of(
                                                          context)
                                                      .getText(
                                                    '6lskrmg7' /* Save */,
                                                  ),
                                                  options: FFButtonOptions(
                                                    width: 90,
                                                    height: 40,
                                                    color: FlutterFlowTheme.of(
                                                            context)
                                                        .secondaryColor,
                                                    textStyle: FlutterFlowTheme
                                                            .of(context)
                                                        .subtitle2
                                                        .override(
                                                          fontFamily: 'Roboto',
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .primaryText,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                    borderSide: BorderSide(
                                                      color: Colors.transparent,
                                                      width: 1,
                                                    ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            20),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding:
                                EdgeInsetsDirectional.fromSTEB(0, 24, 0, 0),
                            child: SingleChildScrollView(
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        24, 24, 24, 0),
                                    child: TextFormField(
                                      controller: bankNameController,
                                      obscureText: false,
                                      decoration: InputDecoration(
                                        labelText:
                                            FFLocalizations.of(context).getText(
                                          'hj1invkb' /* Bank Name */,
                                        ),
                                        hintText:
                                            FFLocalizations.of(context).getText(
                                          'j539pq0x' /* Input your bank name */,
                                        ),
                                        hintStyle: FlutterFlowTheme.of(context)
                                            .bodyText2,
                                        enabledBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        errorBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        focusedErrorBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        filled: true,
                                        fillColor: FlutterFlowTheme.of(context)
                                            .secondaryBackground,
                                      ),
                                      style: FlutterFlowTheme.of(context)
                                          .bodyText1
                                          .override(
                                            fontFamily: 'Roboto',
                                            fontWeight: FontWeight.normal,
                                          ),
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        24, 24, 24, 0),
                                    child: TextFormField(
                                      controller: bankAccountController,
                                      obscureText: false,
                                      decoration: InputDecoration(
                                        labelText:
                                            FFLocalizations.of(context).getText(
                                          '7gubwmhp' /* Bank Account */,
                                        ),
                                        hintText:
                                            FFLocalizations.of(context).getText(
                                          '2fxlvs3x' /* Input your bank account */,
                                        ),
                                        hintStyle: FlutterFlowTheme.of(context)
                                            .bodyText2,
                                        enabledBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        errorBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        focusedErrorBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        filled: true,
                                        fillColor: FlutterFlowTheme.of(context)
                                            .secondaryBackground,
                                      ),
                                      style: FlutterFlowTheme.of(context)
                                          .bodyText1
                                          .override(
                                            fontFamily: 'Roboto',
                                            fontWeight: FontWeight.normal,
                                          ),
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        24, 24, 24, 24),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        FFButtonWidget(
                                          onPressed: () async {
                                            if (user?.role != 'guest')
                                              await saveBankInfomation(
                                                  authProvider);
                                          },
                                          text: FFLocalizations.of(context)
                                              .getText(
                                            '6g5gwv5a' /* Save Bank Information */,
                                          ),
                                          options: FFButtonOptions(
                                            width: 240,
                                            height: 48,
                                            color: FlutterFlowTheme.of(context)
                                                .tertiaryColor,
                                            textStyle: FlutterFlowTheme.of(
                                                    context)
                                                .subtitle2
                                                .override(
                                                  fontFamily: 'Roboto',
                                                  color: FlutterFlowTheme.of(
                                                          context)
                                                      .primaryText,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                            borderSide: BorderSide(
                                              color: Colors.transparent,
                                              width: 1,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(24),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        24, 24, 24, 0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          FFLocalizations.of(context).getText(
                                            'itezvp2g' /* Your Balance: */,
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .bodyText1
                                              .override(
                                                fontFamily: 'Roboto',
                                                fontWeight: FontWeight.w500,
                                              ),
                                        ),
                                        Text(
                                          // FFLocalizations.of(context).getText(
                                          //   'vlnvjiit' /* 5,000 */,
                                          // ),
                                          '${getFormatedNumber(user?.balance)}',
                                          style: FlutterFlowTheme.of(context)
                                              .bodyText1
                                              .override(
                                                fontFamily: 'Roboto',
                                                fontWeight: FontWeight.w500,
                                              ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        24, 24, 24, 0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0, 0, 24, 0),
                                          child: Text(
                                            FFLocalizations.of(context).getText(
                                              '457cm8ge' /* Transfer Mode */,
                                            ),
                                            style: FlutterFlowTheme.of(context)
                                                .bodyText1,
                                          ),
                                        ),
                                        FlutterFlowRadioButton(
                                          options: [
                                            FFLocalizations.of(context).getText(
                                              'xasui136' /* Withdraw */,
                                            ),
                                            FFLocalizations.of(context).getText(
                                              '61ykomo9' /* Deposit */,
                                            )
                                          ].toList(),
                                          initialValue:
                                              FFLocalizations.of(context)
                                                  .getText(
                                            'x3ynxl9l' /* Withdraw */,
                                          ),
                                          onChanged: (val) => setState(
                                              () => radioButtonValue2 = val),
                                          optionHeight: 28,
                                          textStyle: FlutterFlowTheme.of(
                                                  context)
                                              .bodyText1
                                              .override(
                                                fontFamily: 'Roboto',
                                                color:
                                                    FlutterFlowTheme.of(context)
                                                        .primaryText,
                                                fontWeight: FontWeight.normal,
                                              ),
                                          selectedTextStyle: FlutterFlowTheme
                                                  .of(context)
                                              .bodyText1
                                              .override(
                                                fontFamily: 'Roboto',
                                                color:
                                                    FlutterFlowTheme.of(context)
                                                        .secondaryColor,
                                                fontWeight: FontWeight.normal,
                                              ),
                                          buttonPosition:
                                              RadioButtonPosition.left,
                                          direction: Axis.vertical,
                                          radioButtonColor:
                                              FlutterFlowTheme.of(context)
                                                  .secondaryColor,
                                          inactiveRadioButtonColor:
                                              FlutterFlowTheme.of(context)
                                                  .secondaryColor,
                                          toggleable: false,
                                          horizontalAlignment:
                                              WrapAlignment.start,
                                          verticalAlignment:
                                              WrapCrossAlignment.start,
                                        ),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        24, 24, 24, 0),
                                    child: TextFormField(
                                      controller: amountController,
                                      obscureText: false,
                                      decoration: InputDecoration(
                                        labelText:
                                            FFLocalizations.of(context).getText(
                                          'cu722qcq' /* Transfer Amount */,
                                        ),
                                        hintText:
                                            FFLocalizations.of(context).getText(
                                          'cujx3kjv' /* Input transfer amount */,
                                        ),
                                        hintStyle: FlutterFlowTheme.of(context)
                                            .bodyText2,
                                        enabledBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        errorBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        focusedErrorBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        filled: true,
                                        fillColor: FlutterFlowTheme.of(context)
                                            .secondaryBackground,
                                      ),
                                      style: FlutterFlowTheme.of(context)
                                          .bodyText1
                                          .override(
                                            fontFamily: 'Roboto',
                                            fontWeight: FontWeight.normal,
                                          ),
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        24, 24, 24, 24),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        FFButtonWidget(
                                          onPressed: () async {
                                            print('Button pressed ...');
                                          },
                                          text: FFLocalizations.of(context)
                                              .getText(
                                            'xmotmdwz' /* Transactions */,
                                          ),
                                          options: FFButtonOptions(
                                            width: 140,
                                            height: 48,
                                            color: FlutterFlowTheme.of(context)
                                                .secondaryBackground,
                                            textStyle: FlutterFlowTheme.of(
                                                    context)
                                                .subtitle2
                                                .override(
                                                  fontFamily: 'Roboto',
                                                  color: FlutterFlowTheme.of(
                                                          context)
                                                      .primaryText,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                            borderSide: BorderSide(
                                              color: Colors.transparent,
                                              width: 1,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(24),
                                          ),
                                        ),
                                        FFButtonWidget(
                                          onPressed: () async {
                                            debugPrint(radioButtonValue2);
                                            if (radioButtonValue2 == null ||
                                                radioButtonValue2 ==
                                                    FFLocalizations.of(context)
                                                        .getText(
                                                      'xasui136' /* Withdraw */,
                                                    )) {
                                              await _requestWithdraw(
                                                  authProvider);
                                            } else if (radioButtonValue2 ==
                                                FFLocalizations.of(context)
                                                    .getText(
                                                  '61ykomo9' /* Deposit */,
                                                )) {
                                              await _depositRequest(
                                                  authProvider);
                                            }
                                            print('Button pressed ...');
                                          },
                                          text: FFLocalizations.of(context)
                                              .getText(
                                            'plkglzbn' /* Approve */,
                                          ),
                                          options: FFButtonOptions(
                                            width: 140,
                                            height: 48,
                                            color: FlutterFlowTheme.of(context)
                                                .tertiaryColor,
                                            textStyle: FlutterFlowTheme.of(
                                                    context)
                                                .subtitle2
                                                .override(
                                                  fontFamily: 'Roboto',
                                                  color: FlutterFlowTheme.of(
                                                          context)
                                                      .primaryText,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                            borderSide: BorderSide(
                                              color: Colors.transparent,
                                              width: 1,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(24),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Padding(
                            padding:
                                EdgeInsetsDirectional.fromSTEB(0, 24, 0, 0),
                            child: SingleChildScrollView(
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        24, 0, 24, 0),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0, 24, 0, 0),
                                          child: Container(
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                0.8,
                                            height: 40,
                                            decoration: BoxDecoration(
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryBackground,
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.max,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                InkWell(
                                                  onTap: () async {
                                                    context
                                                        .pushNamed('dashboard');
                                                  },
                                                  child: Icon(
                                                    Icons.arrow_back_sharp,
                                                    color: FlutterFlowTheme.of(
                                                            context)
                                                        .secondaryColor,
                                                    size: 24,
                                                  ),
                                                ),
                                                Padding(
                                                  padding: EdgeInsetsDirectional
                                                      .fromSTEB(4, 0, 24, 0),
                                                  child: InkWell(
                                                    onTap: () async {
                                                      context.pushNamed(
                                                          'dashboard');
                                                    },
                                                    child: Text(
                                                      FFLocalizations.of(
                                                              context)
                                                          .getText(
                                                        'p1d9hyzf' /* Go Dashboard */,
                                                      ),
                                                      style:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .bodyText1
                                                              .override(
                                                                fontFamily:
                                                                    'Roboto',
                                                                color: FlutterFlowTheme.of(
                                                                        context)
                                                                    .secondaryColor,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .normal,
                                                              ),
                                                    ),
                                                  ),
                                                ),
                                                // Text(
                                                //   FFLocalizations.of(context)
                                                //       .getText(
                                                //     'sl6li2q0' /* Did you completed? */,
                                                //   ),
                                                //   style: FlutterFlowTheme.of(
                                                //           context)
                                                //       .bodyText1
                                                //       .override(
                                                //         fontFamily: 'Roboto',
                                                //         fontWeight:
                                                //             FontWeight.normal,
                                                //       ),
                                                // ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0, 48, 0, 0),
                                          child: TextFormField(
                                            controller: oldPasswordController,
                                            obscureText: !oldPasswordVisibility,
                                            decoration: InputDecoration(
                                              labelText:
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                'si30btkx' /* Old Password */,
                                              ),
                                              hintText:
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                'kjor6j4q' /* Input your previous password */,
                                              ),
                                              hintStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyText2,
                                              enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              errorBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              focusedErrorBorder:
                                                  OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              filled: true,
                                              fillColor:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryBackground,
                                              suffixIcon: InkWell(
                                                onTap: () => setState(
                                                  () => oldPasswordVisibility =
                                                      !oldPasswordVisibility,
                                                ),
                                                focusNode: FocusNode(
                                                    skipTraversal: true),
                                                child: Icon(
                                                  oldPasswordVisibility
                                                      ? Icons
                                                          .visibility_outlined
                                                      : Icons
                                                          .visibility_off_outlined,
                                                  color: Color(0xFF757575),
                                                  size: 22,
                                                ),
                                              ),
                                            ),
                                            style: FlutterFlowTheme.of(context)
                                                .bodyText1
                                                .override(
                                                  fontFamily: 'Roboto',
                                                  fontWeight: FontWeight.normal,
                                                ),
                                          ),
                                        ),
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0, 20, 0, 0),
                                          child: TextFormField(
                                            controller: passwordController,
                                            obscureText: !passwordVisibility,
                                            decoration: InputDecoration(
                                              labelText:
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                '0cmr7qxg' /* Password */,
                                              ),
                                              hintText:
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                'q6l7lb9c' /* Input your new password */,
                                              ),
                                              hintStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyText2,
                                              enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              errorBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              focusedErrorBorder:
                                                  OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              filled: true,
                                              fillColor:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryBackground,
                                              suffixIcon: InkWell(
                                                onTap: () => setState(
                                                  () => passwordVisibility =
                                                      !passwordVisibility,
                                                ),
                                                focusNode: FocusNode(
                                                    skipTraversal: true),
                                                child: Icon(
                                                  passwordVisibility
                                                      ? Icons
                                                          .visibility_outlined
                                                      : Icons
                                                          .visibility_off_outlined,
                                                  color: Color(0xFF757575),
                                                  size: 20,
                                                ),
                                              ),
                                            ),
                                            style: FlutterFlowTheme.of(context)
                                                .bodyText1
                                                .override(
                                                  fontFamily: 'Roboto',
                                                  fontWeight: FontWeight.normal,
                                                ),
                                          ),
                                        ),
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0, 20, 0, 0),
                                          child: TextFormField(
                                            controller:
                                                confirmPasswordController,
                                            obscureText:
                                                !confirmPasswordVisibility,
                                            decoration: InputDecoration(
                                              labelText:
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                'evjqb557' /* Confirm Password */,
                                              ),
                                              hintText:
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                '7sxdyl7o' /* Input  confirm new password */,
                                              ),
                                              hintStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyText2,
                                              enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              errorBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              focusedErrorBorder:
                                                  OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              filled: true,
                                              fillColor:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryBackground,
                                              suffixIcon: InkWell(
                                                onTap: () => setState(
                                                  () => confirmPasswordVisibility =
                                                      !confirmPasswordVisibility,
                                                ),
                                                focusNode: FocusNode(
                                                    skipTraversal: true),
                                                child: Icon(
                                                  confirmPasswordVisibility
                                                      ? Icons
                                                          .visibility_outlined
                                                      : Icons
                                                          .visibility_off_outlined,
                                                  color: Color(0xFF757575),
                                                  size: 20,
                                                ),
                                              ),
                                            ),
                                            style: FlutterFlowTheme.of(context)
                                                .bodyText1
                                                .override(
                                                  fontFamily: 'Roboto',
                                                  fontWeight: FontWeight.normal,
                                                ),
                                          ),
                                        ),
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0, 32, 0, 32),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.max,
                                            mainAxisAlignment:
                                                MainAxisAlignment.end,
                                            children: [
                                              Padding(
                                                padding: EdgeInsetsDirectional
                                                    .fromSTEB(0, 0, 24, 0),
                                                child: FFButtonWidget(
                                                  onPressed: () async {
                                                    if (user?.role != 'guest')
                                                      await updatePassword(
                                                          authProvider);
                                                  },
                                                  text: FFLocalizations.of(
                                                          context)
                                                      .getText(
                                                    'h43pf8gl' /* Save Change */,
                                                  ),
                                                  options: FFButtonOptions(
                                                    width: 160,
                                                    height: 40,
                                                    color: FlutterFlowTheme.of(
                                                            context)
                                                        .secondaryColor,
                                                    textStyle: FlutterFlowTheme
                                                            .of(context)
                                                        .subtitle2
                                                        .override(
                                                          fontFamily: 'Roboto',
                                                          color: Colors.white,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                    borderSide: BorderSide(
                                                      color: Colors.transparent,
                                                      width: 1,
                                                    ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            20),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Padding(
                            padding:
                                EdgeInsetsDirectional.fromSTEB(20, 20, 20, 0),
                            child: SingleChildScrollView(
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        0, 0, 0, 20),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        // Text(
                                        //   FFLocalizations.of(context).getText(
                                        //     'pgef1knd' /* Select  Devices */,
                                        //   ),
                                        //   style: FlutterFlowTheme.of(context)
                                        //       .bodyText1,
                                        // ),
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  12, 0, 0, 0),
                                          child: FlutterFlowRadioButton(
                                            options: devices,
                                            onChanged: (val) {
                                              setState(() =>
                                                  radioButtonValue3 = val);
                                              try {
                                                Device? _selectedDevice = user
                                                    ?.devices
                                                    ?.where((e) =>
                                                        e.deviceName == val ||
                                                        e.deviceNumber == val)
                                                    .first;

                                                if (_selectedDevice != null) {
                                                  deviceNameController?.text =
                                                      _selectedDevice
                                                          .deviceName;
                                                  deviceNumberController?.text =
                                                      _selectedDevice
                                                          .deviceNumber;
                                                  setState(() {
                                                    selectedDevice =
                                                        _selectedDevice;
                                                    deviceMode = 'edit';
                                                    radioButtonValue4 =
                                                        selectedDevice!.type
                                                            .toUpperCase();
                                                    debugPrint(
                                                        radioButtonValue4);
                                                    switchValue =
                                                        selectedDevice!
                                                            .isDefault;
                                                    dropDownValue =
                                                        selectedDevice!.uix;
                                                  });
                                                }
                                              } catch (err) {
                                                setState(() {
                                                  deviceMode = 'new';
                                                });
                                                deviceNameController?.text = "";
                                                deviceNumberController?.text =
                                                    "";
                                              }
                                            },
                                            optionHeight: 25,
                                            textStyle: FlutterFlowTheme.of(
                                                    context)
                                                .bodyText1
                                                .override(
                                                  fontFamily: 'Roboto',
                                                  color: FlutterFlowTheme.of(
                                                          context)
                                                      .primaryText,
                                                  fontWeight: FontWeight.normal,
                                                ),
                                            selectedTextStyle:
                                                FlutterFlowTheme.of(context)
                                                    .bodyText1
                                                    .override(
                                                      fontFamily: 'Roboto',
                                                      color:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .secondaryColor,
                                                      fontWeight:
                                                          FontWeight.normal,
                                                    ),
                                            buttonPosition:
                                                RadioButtonPosition.left,
                                            direction: Axis.vertical,
                                            radioButtonColor:
                                                FlutterFlowTheme.of(context)
                                                    .secondaryColor,
                                            inactiveRadioButtonColor:
                                                FlutterFlowTheme.of(context)
                                                    .secondaryColor,
                                            toggleable: false,
                                            horizontalAlignment:
                                                WrapAlignment.start,
                                            verticalAlignment:
                                                WrapCrossAlignment.start,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Divider(
                                    thickness: 1,
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        0, 20, 0, 0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          child: TextFormField(
                                            controller: deviceNameController,
                                            obscureText: false,
                                            decoration: InputDecoration(
                                              labelText:
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                '4zruloja' /* Device Name */,
                                              ),
                                              hintText:
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                '6rtja7rs' /* Input device alias */,
                                              ),
                                              hintStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyText2,
                                              enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              errorBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              focusedErrorBorder:
                                                  OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              filled: true,
                                              fillColor:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryBackground,
                                            ),
                                            style: FlutterFlowTheme.of(context)
                                                .bodyText1
                                                .override(
                                                  fontFamily: 'Roboto',
                                                  fontWeight: FontWeight.normal,
                                                ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        0, 20, 0, 0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          child: TextFormField(
                                            controller: deviceNumberController,
                                            obscureText: false,
                                            decoration: InputDecoration(
                                              labelText:
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                'sfdx8clw' /* Device Number */,
                                              ),
                                              hintText:
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                '5fu32vdy' /* Input device number */,
                                              ),
                                              hintStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyText2,
                                              enabledBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              errorBorder: OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              focusedErrorBorder:
                                                  OutlineInputBorder(
                                                borderSide: BorderSide(
                                                  color: Color(0x00000000),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              filled: true,
                                              fillColor:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryBackground,
                                            ),
                                            style: FlutterFlowTheme.of(context)
                                                .bodyText1
                                                .override(
                                                  fontFamily: 'Roboto',
                                                  fontWeight: FontWeight.normal,
                                                ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        0, 20, 0, 0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              FFLocalizations.of(context)
                                                  .getText(
                                                '8xfbzdi1' /* Device Type */,
                                              ),
                                              style:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyText1,
                                            ),
                                            Padding(
                                              padding: EdgeInsetsDirectional
                                                  .fromSTEB(12, 0, 0, 0),
                                              child: FlutterFlowRadioButton(
                                                options: [
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                    '8chaw135' /* 4G */,
                                                  ),
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                    '2qm767bq' /* SMS */,
                                                  )
                                                ].toList(),
                                                groupValue:
                                                    // selectedDevice?.type ??
                                                    radioButtonValue4,
                                                initialValue:
                                                    selectedDevice?.type ??
                                                        '4G',
                                                onChanged: (val) => setState(
                                                    () => radioButtonValue4 =
                                                        val!),
                                                optionHeight: 25,
                                                textStyle:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyText1
                                                        .override(
                                                          fontFamily: 'Roboto',
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .primaryText,
                                                          fontWeight:
                                                              FontWeight.normal,
                                                        ),
                                                selectedTextStyle:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyText1
                                                        .override(
                                                          fontFamily: 'Roboto',
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .secondaryColor,
                                                          fontWeight:
                                                              FontWeight.normal,
                                                        ),
                                                buttonPosition:
                                                    RadioButtonPosition.left,
                                                direction: Axis.vertical,
                                                radioButtonColor:
                                                    FlutterFlowTheme.of(context)
                                                        .secondaryColor,
                                                inactiveRadioButtonColor:
                                                    FlutterFlowTheme.of(context)
                                                        .secondaryColor,
                                                toggleable: false,
                                                horizontalAlignment:
                                                    WrapAlignment.start,
                                                verticalAlignment:
                                                    WrapCrossAlignment.start,
                                              ),
                                            ),
                                          ],
                                        ),
                                        Row(
                                          mainAxisSize: MainAxisSize.max,
                                          children: [
                                            Switch(
                                              value: switchValue ??= true,
                                              onChanged: (newValue) async {
                                                setState(() =>
                                                    switchValue = newValue);
                                              },
                                              activeColor:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryColor,
                                            ),
                                            Text(
                                              FFLocalizations.of(context)
                                                  .getText(
                                                'dnfuzw8y' /* Is Default */,
                                              ),
                                              style:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyText1,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        0, 20, 0, 0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          child: FlutterFlowDropDown<String>(
                                            options: [
                                              'CarV1.1',
                                              'CarV1.2',
                                              'Car2.1',
                                              'Car2.2',
                                              'Chip',
                                              'moped',
                                            ],
                                            optionLabels: [
                                              FFLocalizations.of(context)
                                                  .getText(
                                                'lf59nky3' /* Car V1.1 */,
                                              ),
                                              FFLocalizations.of(context)
                                                  .getText(
                                                'wue84sbf' /* Car V1.2 */,
                                              ),
                                              FFLocalizations.of(context)
                                                  .getText(
                                                'stvvvlf9' /* Asa 1.1 */,
                                              ),
                                              FFLocalizations.of(context)
                                                  .getText(
                                                '9hlinfq3' /* Asa 1.2 */,
                                              ),
                                              'Chip',
                                              'Moped', // Here's the added label for 'moped'
                                            ],
                                            initialOption: dropDownValue,
                                            valueOption: dropDownValue,
                                            onChanged: (val) {
                                              setState(() {
                                                dropDownValue = val;
                                              });
                                            },
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                0.01,
                                            height: 50,
                                            textStyle: FlutterFlowTheme.of(
                                                    context)
                                                .bodyText1
                                                .override(
                                                  fontFamily: 'Roboto',
                                                  color: FlutterFlowTheme.of(
                                                          context)
                                                      .primaryText,
                                                  fontWeight: FontWeight.normal,
                                                ),
                                            hintText:
                                                FFLocalizations.of(context)
                                                    .getText(
                                              'm7uy5vst' /* Please select uix... */,
                                            ),
                                            fillColor:
                                                FlutterFlowTheme.of(context)
                                                    .secondaryBackground,
                                            elevation: 2,
                                            borderColor: Colors.transparent,
                                            borderWidth: 0,
                                            borderRadius: 8,
                                            margin:
                                                EdgeInsetsDirectional.fromSTEB(
                                                    12, 4, 12, 4),
                                            hidesUnderline: true,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        0, 20, 0, 20),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          child: Padding(
                                            padding:
                                                EdgeInsetsDirectional.fromSTEB(
                                                    0, 0, 4, 0),
                                            child: FFButtonWidget(
                                              onPressed: () async {
                                                if (user?.role != 'guest')
                                                  await saveDevice(
                                                      authProvider);
                                              },
                                              text: FFLocalizations.of(context)
                                                  .getText(
                                                '8o9d116q' /* Save */,
                                              ),
                                              options: FFButtonOptions(
                                                width: 130,
                                                height: 40,
                                                color:
                                                    FlutterFlowTheme.of(context)
                                                        .secondaryColor,
                                                textStyle:
                                                    FlutterFlowTheme.of(context)
                                                        .subtitle2
                                                        .override(
                                                          fontFamily: 'Roboto',
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .primaryText,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                borderSide: BorderSide(
                                                  color: Colors.transparent,
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(20),
                                              ),
                                            ),
                                          ),
                                        ),
                                        Expanded(
                                          child: Padding(
                                            padding:
                                                EdgeInsetsDirectional.fromSTEB(
                                                    4, 0, 0, 0),
                                            child: FFButtonWidget(
                                              onPressed: () async {
                                                if (user?.role != 'guest')
                                                  await deleteDevice(
                                                      authProvider);
                                              },
                                              text: FFLocalizations.of(context)
                                                  .getText(
                                                '6uu4xjgr' /* Delete */,
                                              ),
                                              options: FFButtonOptions(
                                                width: 130,
                                                height: 40,
                                                color:
                                                    FlutterFlowTheme.of(context)
                                                        .tertiaryColor,
                                                textStyle:
                                                    FlutterFlowTheme.of(context)
                                                        .subtitle2
                                                        .override(
                                                          fontFamily: 'Roboto',
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .primaryText,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                borderSide: BorderSide(
                                                  color: Colors.transparent,
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(20),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
